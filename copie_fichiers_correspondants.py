import os
import shutil
import json
from pathlib import Path
from tkinter import Tk, filedialog, messagebox

# === Constantes ===
CONFIG_FILE = "last_paths_correspondants.json"

def charger_chemins():
    chemins_par_defaut = {
        "source": str(Path.home()),
        "reference": str(Path.home()),
        "destination_c": str(Path.home()),
        "destination_d": str(Path.home())
    }
    
    if Path(CONFIG_FILE).exists():
        try:
            with open(CONFIG_FILE, "r") as f:
                chemins = json.load(f)
                # S'assurer que toutes les clés nécessaires existent
                for key in chemins_par_defaut:
                    if key not in chemins:
                        chemins[key] = chemins_par_defaut[key]
                return chemins
        except:
            return chemins_par_defaut
    return chemins_par_defaut

def sauvegarder_chemins(source, reference, destination_c, destination_d):
    chemins = {
        "source": str(source),
        "reference": str(reference),
        "destination_c": str(destination_c),
        "destination_d": str(destination_d)
    }
    with open(CONFIG_FILE, "w") as f:
        json.dump(chemins, f)

def copier_fichiers_correspondants():
    # Initialiser Tkinter
    root = Tk()
    root.withdraw()

    # Charger les derniers chemins
    chemins = charger_chemins()

    # Sélectionner les dossiers
    dossier_a = Path(filedialog.askdirectory(title="Sélectionner le dossier source (A)", initialdir=chemins["source"]))
    if not dossier_a:
        return False

    dossier_b = Path(filedialog.askdirectory(title="Sélectionner le dossier de référence (B)", initialdir=chemins["reference"]))
    if not dossier_b:
        return False

    dossier_c = Path(filedialog.askdirectory(title="Sélectionner le dossier destination C", initialdir=chemins["destination_c"]))
    if not dossier_c:
        return False

    dossier_d = Path(filedialog.askdirectory(title="Sélectionner le dossier destination D", initialdir=chemins["destination_d"]))
    if not dossier_d:
        return False

    # Sauvegarder les chemins sélectionnés
    sauvegarder_chemins(dossier_a, dossier_b, dossier_c, dossier_d)

    # Utiliser le nom du dossier A comme préfixe
    prefixe = dossier_a.name + "_"

    # Créer les dossiers de destination s'ils n'existent pas
    dossier_c.mkdir(parents=True, exist_ok=True)
    dossier_d.mkdir(parents=True, exist_ok=True)

    # Obtenir la liste des fichiers dans le dossier B
    fichiers_b = {f.name for f in dossier_b.iterdir() if f.is_file()}

    # Compteurs pour les fichiers copiés
    fichiers_copies_c = 0
    fichiers_copies_d = 0

    # Parcourir les fichiers du dossier A
    for fichier in dossier_a.iterdir():
        if fichier.is_file() and fichier.name in fichiers_b:
            # Copier le fichier du dossier A vers C
            nouveau_nom = f"{prefixe}{fichier.name}"
            destination = dossier_c / nouveau_nom
            shutil.copy2(fichier, destination)
            fichiers_copies_c += 1

            # Copier le fichier correspondant du dossier B vers D
            fichier_b = dossier_b / fichier.name
            destination_d = dossier_d / nouveau_nom
            shutil.copy2(fichier_b, destination_d)
            fichiers_copies_d += 1

    # Afficher le résultat
    messagebox.showinfo("Résultat", 
                       f"Opération terminée.\n"
                       f"Nombre de fichiers copiés dans C : {fichiers_copies_c}\n"
                       f"Nombre de fichiers copiés dans D : {fichiers_copies_d}\n"
                       f"Vers les dossiers :\n"
                       f"C : {dossier_c}\n"
                       f"D : {dossier_d}")

    return True

if __name__ == "__main__":
    copier_fichiers_correspondants() 