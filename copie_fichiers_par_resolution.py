import os
import shutil
import json
import random
from pathlib import Path
from PIL import Image
from tkinter import Tk, filedialog, messagebox

CONFIG_FILE = "last_paths_correspondants.json"

def charger_chemins():
    chemins_par_defaut = {
        "source": str(Path.home()),
        "reference": str(Path.home()),
        "destination_c": str(Path.home()),
        "destination_d": str(Path.home())
    }
    if Path(CONFIG_FILE).exists():
        try:
            with open(CONFIG_FILE, "r") as f:
                chemins = json.load(f)
                for key in chemins_par_defaut:
                    if key not in chemins:
                        chemins[key] = chemins_par_defaut[key]
                return chemins
        except:
            return chemins_par_defaut
    return chemins_par_defaut

def sauvegarder_chemins(source, reference, destination_c, destination_d):
    chemins = {
        "source": str(source),
        "reference": str(reference),
        "destination_c": str(destination_c),
        "destination_d": str(destination_d)
    }
    with open(CONFIG_FILE, "w") as f:
        json.dump(chemins, f)

def obtenir_resolution(fichier):
    try:
        with Image.open(fichier) as img:
            return img.size  # (largeur, hauteur)
    except Exception:
        return None

def copier_fichiers_par_resolution():
    root = Tk()
    root.withdraw()
    chemins = charger_chemins()
    dossier_a = Path(filedialog.askdirectory(title="Sélectionner le dossier source (A)", initialdir=chemins["source"]))
    if not dossier_a:
        return False
    dossier_b = Path(filedialog.askdirectory(title="Sélectionner le dossier de référence (B)", initialdir=chemins["reference"]))
    if not dossier_b:
        return False
    dossier_c = Path(filedialog.askdirectory(title="Sélectionner le dossier destination C", initialdir=chemins["destination_c"]))
    if not dossier_c:
        return False
    dossier_d = Path(filedialog.askdirectory(title="Sélectionner le dossier destination D", initialdir=chemins["destination_d"]))
    if not dossier_d:
        return False
    sauvegarder_chemins(dossier_a, dossier_b, dossier_c, dossier_d)
    prefixe = dossier_a.name + "_"
    dossier_c.mkdir(parents=True, exist_ok=True)
    dossier_d.mkdir(parents=True, exist_ok=True)
    fichiers_b = {f.name for f in dossier_b.iterdir() if f.is_file()}
    # Grouper les fichiers de A par résolution
    groupes = {}
    for fichier in dossier_a.iterdir():
        if fichier.is_file():
            res = obtenir_resolution(fichier)
            if res:
                groupes.setdefault(res, []).append(fichier)
    fichiers_copies_c = 0
    fichiers_copies_d = 0
    for res, fichiers in groupes.items():
        selection = random.sample(fichiers, min(3, len(fichiers)))
        for fichier in selection:
            if fichier.name in fichiers_b:
                nouveau_nom = f"{prefixe}{fichier.name}"
                destination = dossier_c / nouveau_nom
                shutil.copy2(fichier, destination)
                fichiers_copies_c += 1
                fichier_b = dossier_b / fichier.name
                destination_d = dossier_d / nouveau_nom
                shutil.copy2(fichier_b, destination_d)
                fichiers_copies_d += 1
    messagebox.showinfo("Résultat",
        f"Opération terminée.\n"
        f"Nombre de fichiers copiés dans C : {fichiers_copies_c}\n"
        f"Nombre de fichiers copiés dans D : {fichiers_copies_d}\n"
        f"Vers les dossiers :\n"
        f"C : {dossier_c}\n"
        f"D : {dossier_d}")
    return True

if __name__ == "__main__":
    try:
        from PIL import Image
    except ImportError:
        import sys
        messagebox.showerror("Erreur", "Le module Pillow (PIL) est requis. Installez-le avec 'pip install pillow'.")
        sys.exit(1)
    copier_fichiers_par_resolution() 