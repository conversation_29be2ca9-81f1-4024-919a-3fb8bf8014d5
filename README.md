# Copie Aléatoire d'Images

Ce programme permet de copier ou déplacer aléatoirement un nombre défini d'images d'un dossier source vers un dossier destination.

## Création de l'exécutable

Pour créer l'exécutable :

1. Assurez-vous d'avoir Python installé sur votre système
2. Exécutez le script de build :
   ```
   python build.py
   ```
3. L'exécutable sera créé dans le dossier `dist`

## Utilisation

1. Double-cliquez sur `CopieAleatoire.exe` dans le dossier `dist`
2. Sélectionnez le dossier source contenant les images
3. Sélectionnez le dossier destination où vous souhaitez copier/déplacer les images
4. Le programme copiera/déplacera aléatoirement le nombre d'images défini dans le code

## Configuration

Vous pouvez modifier les paramètres suivants dans le fichier `copie_aleatoire.py` :

- `nombre_images` : nombre d'images à copier/déplacer
- `copie` : 1 pour copier, 0 pour déplacer
- `ajout_suffixe` : 1 pour ajouter le nom du dossier source en suffixe, 0 pour l'ajouter en préfixe 