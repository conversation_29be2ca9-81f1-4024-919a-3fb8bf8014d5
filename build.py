import os
import subprocess
import sys

def install_requirements():
    print("Installation des dépendances...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])

def create_executable():
    print("Création de l'exécutable...")
    # Création de l'exécutable avec une icône et en mode onefile
    subprocess.check_call([
        "pyinstaller",
        "--name=CopieAleatoire",
        "--onefile",
        "--noconsole",
        "--clean",
        "copie_aleatoire.py"
    ])

if __name__ == "__main__":
    install_requirements()
    create_executable()
    print("\nL'exécutable a été créé dans le dossier 'dist'")
    print("Vous pouvez trouver 'CopieAleatoire.exe' dans ce dossier") 